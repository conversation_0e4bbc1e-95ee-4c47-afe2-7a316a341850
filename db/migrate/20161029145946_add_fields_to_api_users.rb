class AddFieldsToApiUsers < ActiveRecord::Migration[4.2]
  def change
    add_column :api_users, :static_authentication_token, :string
    add_column :api_users, :static_authentication_token_expires_at, :datetime

    add_column :api_users, :next_questionable_profile, :integer
    add_column :api_users, :test_language, :string
    add_column :api_users, :language_name, :string

    add_column :api_users, :pipplet_clients_api_url, :string
    add_column :api_users, :pipplet_clients_api_token, :string

    add_column :api_users, :sync_with_pipplet_clients, :boolean
    add_column :api_users, :pipplet_clients_account_id, :integer
    add_column :api_users, :pipplet_clients_campaign_id, :integer
  end
end
