class AddAnsweredAtToChallengeLogs < ActiveRecord::Migration[4.2]
  def change
    # ChallengeLogs
    add_column :challenge_logs, :production_answered_at, :datetime
    add_column :challenge_logs, :reception_answered_at, :datetime
    add_column :challenge_logs, :production_id, :integer
    add_column :challenge_logs, :reception_id, :integer

    add_index :challenge_logs, %i[rated reception_user_id production_user_id], name: :rated_per_user
    add_index :challenge_logs, %i[reception_user_id production_user_id], name: :per_user
    add_index :challenge_logs, %i[question_element_id reception_user_id production_user_id], name: :qe_per_user
    add_index :challenge_logs, :scoring_batch_id
    add_index :challenge_logs, :reception_id
    add_index :challenge_logs, :production_id

    # PastChallenges
    add_column :past_challenges, :answered_at, :datetime
    add_column :past_challenges, :production_answered_at, :datetime
    add_column :past_challenges, :reception_answered_at, :datetime
    add_column :past_challenges, :production_id, :integer
    add_column :past_challenges, :reception_id, :integer

    rename_column :past_challenges, :production, :is_production
    rename_column :past_challenges, :reception, :is_reception

    add_index :past_challenges, :is_production
    add_index :past_challenges, :is_reception
    add_index :past_challenges, %i[user_id answered_at]
    add_index :past_challenges, %i[user_id rated answered_at]
  end
end
