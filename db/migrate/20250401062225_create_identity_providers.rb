class CreateIdentityProviders < ActiveRecord::Migration[8.0]
  def change
    create_table :identity_providers do |t|
      t.string :name, null: false
      t.string :sp_entity_id, null: false
      t.integer :vendor, null: false, default: 0

      t.string :metadata_url, null: true, default: nil
      t.string :metadata_xml, null: true, default: nil

      t.jsonb :settings, null: false, default: {}
      t.datetime :settings_cached_until, null: true, default: nil
      t.jsonb :assertion_response_options, null: false, default: {}

      t.datetime :deleted_at

      t.timestamps
    end
  end
end
